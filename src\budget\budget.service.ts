import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Budget, BudgetDocument } from './budget.schema';

@Injectable()
export class BudgetService {
    constructor(@InjectModel(Budget.name) private budgetModel: Model<BudgetDocument>,) {}

    async create(budget: Budget): Promise<Budget> {
        const newBudget = new this.budgetModel(budget);
        return newBudget.save();
    }
    async findAll(): Promise<Budget[]> {
        return this.budgetModel.find().exec();
    }

    async findOne(id: string): Promise<Budget> {
        return this.budgetModel.findById(id).exec();
    }

    async update(id: string, budget: Budget): Promise<Budget> {
        return this.budgetModel.findByIdAndUpdate(id, budget, { new: true }).exec();
    }

    async delete(id: string): Promise<Budget> {
        return this.budgetModel.findByIdAndDelete(id).exec();
    }
}
