import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type BudgetDocument = Budget & Document;

@Schema()
export class Budget {
  @Prop()
  account_ID: string;

  @Prop()
  description: string;

  @Prop()
  chart_code: string;

  @Prop()
  short_code: string;

  @Prop()
  currency: string;

  @Prop()
  active: string;

  @Prop()
  end: string;

  @Prop()
  companySite: string;
}

export const BudgetSchema = SchemaFactory.createForClass(Budget);
