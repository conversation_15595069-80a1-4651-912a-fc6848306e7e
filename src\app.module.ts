import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BudgetModule } from './budget/budget.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Connect to MongoDB (local database)
    MongooseModule.forRoot('mongodb://localhost:27017/finance_db'),

    // Register our Budget module
    BudgetModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
